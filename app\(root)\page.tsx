"use client";
import { But<PERSON> } from "@/components/ui/button";
import ROUTES from "@/constants/routes";
import { useRouter } from "next/navigation";

const Home = () => {
  const router = useRouter();
  return (
    <div className="pt-20">
      <h1 className="h1-bold font-inter text-dark-400">
        Tailwind CSS is fun [interfont]
      </h1>
      <h1 className="h1-bold font-space-grotesk">
        Tailwind CSS is fun [spaceGrotesk]
      </h1>
      <form
        className="px-10 pt-[100px]"
        action={() => {
          router.push(ROUTES.SIGN_IN);
        }}
      >
        <Button type="submit">Log out</Button>
      </form>
    </div>
  );
};

export default Home;
