import Image from "next/image";
import Link from "next/link";
import React from "react";
// import Theme from "./Theme";

const Navbar = () => {
  return (
    <nav
      className="flex-between w-full fixed z-50 p-6 background-light900_dark200 
        shadow-light-300 dark:shadow-none 
        sm:px-12 gap-5"
    >
      <Link href={"./"} className="flex items-center gap-1">
        <Image
          src={"/images/logo-light.svg"}
          alt="IntView Logo"
          width={120}
          height={120}
        />
        {/* @TODO: better to use text and icon separately for flexibility on small screen */}
        {/* <p
          className="h2-bold font-space-grotesk text-dark-100 
        dark:text-light-900 max-sm:hidden"
        >
          {" "}
          Intview <span className="text-primary-500">AI</span>
        </p> */}
      </Link>
      <p> Searchbox or anything </p>
      <div className="flex-between gap-5">{/* <Theme /> */}</div>
    </nav>
  );
};

export default Navbar;
