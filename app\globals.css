@import "tailwindcss";
@import "tw-animate-css";

@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

@theme {
  /* Color Palette */
  --color-primary: #6938ef;
  --color-secondary: #946eff;
  --color-dark: #212121;
  --color-light: #1d6cd3;

  /* Typography Variables */
  --font-inter: var(--font-inter);
  --font-space-grotesk: var(--font-space-grotesk);

  /* Radius */
  --radius: 0.5rem;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
}

/* Base styles */
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  :root {
    --radius: 0.5rem;
  }
}

/* Typography Utilities */
.h1-bold {
  font-size: 34px;
  font-weight: bold;
  line-height: 34px;
}

.h2-semibold {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.h3-medium {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
}

.h4-regular {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

/* Utility Classes */
.primary-text {
  background: var(--color-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
